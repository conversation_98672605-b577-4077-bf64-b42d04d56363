import React, { useEffect, useState } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';

interface HeatmapPoint {
  lat: number;
  lng: number;
  intensity: number;
  species_count?: number;
  recording_count?: number;
}

interface HeatmapLayerProps {
  data: HeatmapPoint[];
  options?: {
    radius?: number;
    blur?: number;
    maxZoom?: number;
    max?: number;
    minOpacity?: number;
    gradient?: { [key: string]: string };
  };
  onPointClick?: (point: HeatmapPoint) => void;
}

const HeatmapLayer: React.FC<HeatmapLayerProps> = ({
  data,
  options = {},
  onPointClick,
}) => {
  const map = useMap();
  const [heatLayer, setHeatLayer] = useState<any>(null);



  useEffect(() => {
    if (!map || !data || data.length === 0) return;

    // 移除现有的热力图层
    if (heatLayer) {
      map.removeLayer(heatLayer);
    }

    // 创建图层组来管理所有的圆形标记
    const markerGroup = L.layerGroup();

    data.forEach(point => {
      // 根据强度计算颜色和大小
      const intensity = Math.max(0.1, Math.min(1, point.intensity));
      const radius = 8 + intensity * 15; // 8-23px 半径

      // 根据强度选择颜色（模拟热力图颜色）
      let color: string;
      if (intensity >= 0.8) color = '#d73027';      // 红色 - 极高密度
      else if (intensity >= 0.6) color = '#f46d43'; // 橙色 - 高密度
      else if (intensity >= 0.4) color = '#fdae61'; // 橙黄色 - 中高密度
      else if (intensity >= 0.3) color = '#fee08b'; // 浅黄色 - 中等密度
      else if (intensity >= 0.2) color = '#e6f598'; // 黄绿色 - 中低密度
      else if (intensity >= 0.1) color = '#abdda4'; // 浅绿色 - 低密度
      else color = '#66c2a5';                       // 青绿色 - 极低密度

      const marker = L.circleMarker([point.lat, point.lng], {
        radius: radius,
        fillColor: color,
        color: color,
        weight: 1,
        opacity: 0.8,
        fillOpacity: 0.6,
      });

      // 添加点击事件
      if (onPointClick) {
        marker.on('click', () => {
          onPointClick(point);
        });
      }

      // 添加悬停效果
      marker.on('mouseover', () => {
        marker.setStyle({
          radius: radius + 3,
          weight: 2,
          opacity: 1,
          fillOpacity: 0.8,
        });
      });

      marker.on('mouseout', () => {
        marker.setStyle({
          radius: radius,
          weight: 1,
          opacity: 0.8,
          fillOpacity: 0.6,
        });
      });

      markerGroup.addLayer(marker);
    });

    // 添加到地图
    markerGroup.addTo(map);
    setHeatLayer(markerGroup);

    // 清理函数
    return () => {
      if (markerGroup) {
        map.removeLayer(markerGroup);
      }
    };
  }, [map, data, options, onPointClick]);

  // 根据缩放级别调整标记大小
  useEffect(() => {
    if (!map || !heatLayer) return;

    const handleZoomEnd = () => {
      const zoom = map.getZoom();

      // 根据缩放级别调整标记大小
      heatLayer.eachLayer((layer: any) => {
        if (layer instanceof L.CircleMarker) {
          const currentRadius = layer.options.radius || 15;
          let scaleFactor = 1;

          if (zoom < 5) scaleFactor = 0.7;
          else if (zoom < 8) scaleFactor = 0.85;
          else if (zoom > 12) scaleFactor = 1.2;

          layer.setRadius(currentRadius * scaleFactor);
        }
      });
    };

    map.on('zoomend', handleZoomEnd);

    return () => {
      map.off('zoomend', handleZoomEnd);
    };
  }, [map, heatLayer]);

  return null; // 这个组件不渲染任何 DOM 元素
};

export default HeatmapLayer;

// 工具函数：生成模拟热力图数据
export const generateMockHeatmapData = (count: number = 500): HeatmapPoint[] => {
  const data: HeatmapPoint[] = [];
  
  // 定义一些热点区域（模拟真实的海洋生物分布）
  const hotspots = [
    { center: [35.0, 139.0], name: '日本近海' },      // 日本
    { center: [37.0, -122.0], name: '加州海岸' },     // 加州
    { center: [60.0, -150.0], name: '阿拉斯加湾' },   // 阿拉斯加
    { center: [-35.0, 150.0], name: '澳洲东海岸' },   // 澳洲
    { center: [70.0, -8.0], name: '挪威海' },         // 挪威
    { center: [25.0, -80.0], name: '佛罗里达海峡' },  // 佛罗里达
  ];

  hotspots.forEach(hotspot => {
    const pointsInHotspot = Math.floor(count / hotspots.length);
    
    for (let i = 0; i < pointsInHotspot; i++) {
      // 在热点周围生成随机点
      const lat = hotspot.center[0] + (Math.random() - 0.5) * 10;
      const lng = hotspot.center[1] + (Math.random() - 0.5) * 15;
      
      // 距离热点中心越近，强度越高
      const distance = Math.sqrt(
        Math.pow(lat - hotspot.center[0], 2) + 
        Math.pow(lng - hotspot.center[1], 2)
      );
      const intensity = Math.max(0.1, 1 - distance / 8);
      
      data.push({
        lat,
        lng,
        intensity,
        species_count: Math.floor(intensity * 50),
        recording_count: Math.floor(intensity * 200),
      });
    }
  });

  return data;
};
