import React, { useState, useEffect } from 'react';
import { Input, Space, Button, Typography, Card, Statistic, Row, Col, Tooltip, AutoComplete } from 'antd';
import { SearchOutlined, EnvironmentOutlined, SoundOutlined, InfoCircleOutlined } from '@ant-design/icons';
import HomePageMap from '../components/Map/HomePageMap';

const { Text } = Typography;

const HomePage: React.FC = () => {
  const [searchValue, setSearchValue] = useState('');
  const [searchOptions, setSearchOptions] = useState<Array<{value: string, label: string, type: string}>>([]);
  const [mapData, setMapData] = useState({
    totalSpecies: 0,
    totalRecordings: 0,
    activeHotspots: 0,
  });

  // 模拟数据加载
  useEffect(() => {
    // 这里应该从API获取实际数据
    setMapData({
      totalSpecies: 1247,
      totalRecordings: 8936,
      activeHotspots: 342,
    });
  }, []);

  // 搜索建议处理
  const handleSearchChange = (value: string) => {
    setSearchValue(value);

    if (value.length > 1) {
      // 模拟搜索建议
      const mockSuggestions = [
        { value: '座头鲸', label: '🐋 座头鲸 (Megaptera novaeangliae)', type: 'species' },
        { value: '蓝鲸', label: '🐋 蓝鲸 (Balaenoptera musculus)', type: 'species' },
        { value: '虎鲸', label: '🐋 虎鲸 (Orcinus orca)', type: 'species' },
        { value: '日本近海', label: '📍 日本近海', type: 'location' },
        { value: '加州海岸', label: '📍 加州海岸', type: 'location' },
        { value: '阿拉斯加湾', label: '📍 阿拉斯加湾', type: 'location' },
      ].filter(item =>
        item.value.toLowerCase().includes(value.toLowerCase()) ||
        item.label.toLowerCase().includes(value.toLowerCase())
      );

      setSearchOptions(mockSuggestions);
    } else {
      setSearchOptions([]);
    }
  };

  const handleSearch = async (value: string) => {
    if (!value.trim()) return;

    console.log('搜索:', value);

    try {
      // 这里应该调用搜索API
      // const response = await fetch(`/api/search?q=${encodeURIComponent(value)}`);
      // const results = await response.json();

      // 模拟搜索结果
      const mockResults = [
        { type: 'species', name: '座头鲸', latin: 'Megaptera novaeangliae', lat: 35.0, lng: 139.0 },
        { type: 'location', name: '日本近海', lat: 35.0, lng: 139.0 },
        { type: 'species', name: '蓝鲸', latin: 'Balaenoptera musculus', lat: 37.0, lng: -122.0 },
      ];

      // 如果找到结果，跳转到第一个结果的位置
      if (mockResults.length > 0) {
        const firstResult = mockResults[0];
        // 这里可以通过 ref 或者状态管理来控制地图跳转
        console.log('跳转到:', firstResult);

        // 暂时使用 alert 显示搜索结果
        alert(`找到 ${mockResults.length} 个结果，第一个结果：${firstResult.name}`);
      } else {
        alert('未找到相关结果');
      }
    } catch (error) {
      console.error('搜索失败:', error);
      alert('搜索失败，请稍后重试');
    }
  };

  return (
    <div style={{
      height: 'calc(100vh - 64px)', // 减去header高度
      display: 'flex',
      flexDirection: 'column',
      background: '#f5f5f5'
    }}>
      {/* 顶部搜索栏 */}
      <div style={{
        background: 'white',
        padding: '16px 24px',
        borderBottom: '1px solid #f0f0f0',
        boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
      }}>
        <Row gutter={24} align="middle">
          <Col flex="auto">
            <Space size="large" style={{ width: '100%' }}>
              <div style={{ flex: 1, maxWidth: '500px' }}>
                <AutoComplete
                  options={searchOptions}
                  value={searchValue}
                  onChange={handleSearchChange}
                  onSelect={handleSearch}
                  style={{ width: '100%' }}
                >
                  <Input.Search
                    placeholder="搜索地点、物种名称或拉丁学名..."
                    allowClear
                    enterButton={<SearchOutlined />}
                    size="large"
                    onSearch={handleSearch}
                  />
                </AutoComplete>
              </div>
              <Space>
                <Tooltip title="查看帮助">
                  <Button icon={<InfoCircleOutlined />} type="text" />
                </Tooltip>
              </Space>
            </Space>
          </Col>
          <Col>
            <Space size="large">
              <Statistic
                title="物种数量"
                value={mapData.totalSpecies}
                prefix={<SoundOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ fontSize: '16px', color: '#1890ff' }}
              />
              <Statistic
                title="录音数量"
                value={mapData.totalRecordings}
                prefix={<EnvironmentOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ fontSize: '16px', color: '#52c41a' }}
              />
            </Space>
          </Col>
        </Row>
      </div>

      {/* 主要内容区域 - 地图和侧边栏 */}
      <div style={{
        flex: 1,
        display: 'flex',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* 地图区域 */}
        <div style={{
          flex: 1,
          position: 'relative',
          background: 'white'
        }}>
          <HomePageMap
            height="100%"
            onDataLoad={(stats) => {
              setMapData({
                totalSpecies: stats.totalSpecies,
                totalRecordings: stats.totalRecordings,
                activeHotspots: stats.activeHotspots,
              });
            }}
          />
        </div>

        {/* 右侧信息面板 */}
        <div style={{
          width: '320px',
          background: 'white',
          borderLeft: '1px solid #f0f0f0',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}>
          {/* 图例区域 */}
          <Card
            title={
              <Space>
                <span style={{ fontSize: '14px', fontWeight: 'bold' }}>🎨 数据密度图例</span>
              </Space>
            }
            size="small"
            style={{
              margin: '16px',
              marginBottom: '8px',
              flex: 'none',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}
          >
            <div style={{ display: 'flex', flexDirection: 'column', gap: '6px' }}>
              {[
                { color: '#d73027', range: '80-100%', label: '极高密度', description: '热点区域' },
                { color: '#f46d43', range: '60-80%', label: '高密度', description: '活跃区域' },
                { color: '#fdae61', range: '40-60%', label: '中高密度', description: '常见区域' },
                { color: '#fee08b', range: '30-40%', label: '中等密度', description: '一般区域' },
                { color: '#e6f598', range: '20-30%', label: '中低密度', description: '较少区域' },
                { color: '#abdda4', range: '10-20%', label: '低密度', description: '稀少区域' },
                { color: '#66c2a5', range: '0-10%', label: '极低密度', description: '偶见区域' },
              ].map((item, index) => (
                <div key={index} style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  background: index % 2 === 0 ? '#fafafa' : 'transparent'
                }}>
                  <div style={{
                    width: '18px',
                    height: '18px',
                    backgroundColor: item.color,
                    borderRadius: '50%',
                    border: '2px solid white',
                    boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
                  }} />
                  <div style={{ flex: 1 }}>
                    <div style={{ fontSize: '12px', fontWeight: 'bold' }}>{item.range}</div>
                    <div style={{ fontSize: '10px', color: '#666' }}>{item.description}</div>
                  </div>
                </div>
              ))}
            </div>
            <div style={{
              marginTop: '12px',
              padding: '8px',
              background: '#f0f9ff',
              borderRadius: '4px',
              fontSize: '11px',
              color: '#666'
            }}>
              💡 提示：点击地图上的圆点查看详细信息
            </div>
          </Card>

          {/* 统计信息 */}
          <Card
            title={
              <Space>
                <span style={{ fontSize: '14px', fontWeight: 'bold' }}>📊 实时统计</span>
              </Space>
            }
            size="small"
            style={{
              margin: '8px 16px',
              marginBottom: '8px',
              flex: 'none',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}
          >
            <Row gutter={[8, 16]}>
              <Col span={24}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '8px 12px',
                  background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
                  borderRadius: '6px',
                  color: 'white'
                }}>
                  <div>
                    <div style={{ fontSize: '20px', fontWeight: 'bold' }}>{mapData.activeHotspots}</div>
                    <div style={{ fontSize: '12px', opacity: 0.9 }}>活跃热点</div>
                  </div>
                  <div style={{ fontSize: '24px' }}>🔥</div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ textAlign: 'center', padding: '8px' }}>
                  <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#52c41a' }}>
                    {mapData.totalSpecies}
                  </div>
                  <div style={{ fontSize: '11px', color: '#666' }}>物种数量</div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ textAlign: 'center', padding: '8px' }}>
                  <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#fa8c16' }}>
                    {mapData.totalRecordings}
                  </div>
                  <div style={{ fontSize: '11px', color: '#666' }}>录音数量</div>
                </div>
              </Col>
              <Col span={24}>
                <div style={{
                  padding: '8px',
                  background: '#f6ffed',
                  borderRadius: '4px',
                  border: '1px solid #b7eb8f'
                }}>
                  <div style={{ fontSize: '12px', color: '#52c41a', fontWeight: 'bold' }}>
                    🆕 今日新增: 23 个数据点
                  </div>
                  <div style={{ fontSize: '11px', color: '#666', marginTop: '2px' }}>
                    最后更新: 2 分钟前
                  </div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* 最近活动 */}
          <Card
            title="最近活动"
            size="small"
            style={{
              margin: '8px 16px',
              marginBottom: '16px',
              flex: 1,
              overflow: 'hidden'
            }}
            styles={{
              body: {
                padding: '12px',
                height: '100%',
                overflow: 'auto'
              }
            }}
          >
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {[
                { species: '座头鲸', location: '太平洋', time: '2小时前', type: 'new' },
                { species: '蓝鲸', location: '大西洋', time: '4小时前', type: 'update' },
                { species: '虎鲸', location: '北极海', time: '6小时前', type: 'new' },
                { species: '灰鲸', location: '白令海', time: '8小时前', type: 'update' },
              ].map((item, index) => (
                <div key={index} style={{
                  padding: '8px',
                  background: '#fafafa',
                  borderRadius: '4px',
                  fontSize: '12px'
                }}>
                  <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                    {item.species}
                  </div>
                  <div style={{ color: '#666', marginBottom: '2px' }}>
                    📍 {item.location}
                  </div>
                  <div style={{ color: '#999' }}>
                    🕒 {item.time}
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
